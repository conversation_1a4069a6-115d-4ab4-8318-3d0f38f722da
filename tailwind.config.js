/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        plum: {
          50: '#fdf4ff',
          100: '#fae8ff',
          200: '#f5d0fe',
          300: '#f0abfc',
          400: '#e879f9',
          500: '#d946ef',
          600: '#c026d3',
          700: '#a21caf',
          800: '#86198f',
          900: '#701a75',
        },
        'whitish-plum': {
          50: '#fefcff',
          100: '#fdf9ff',
          200: '#fbf3ff',
          300: '#f8ebff',
          400: '#f3ddff',
          500: '#edcaff',
          600: '#e5b3ff',
          700: '#d999ff',
          800: '#cc7dff',
          900: '#bb5eff',
        },
        'light-pink': {
          50: '#fef7f7',
          100: '#feecec',
          200: '#fdd6d6',
          300: '#fbb5b5',
          400: '#f78888',
          500: '#f06060',
          600: '#de4040',
          700: '#bb2d2d',
          800: '#9b2828',
          900: '#812727',
        }
      },
      animation: {
        'bounce-in': 'bounceIn 0.6s ease-out',
        'fade-in': 'fadeIn 0.5s ease-in',
        'slide-up': 'slideUp 0.4s ease-out',
        'pulse-success': 'pulseSuccess 2s infinite',
      },
      keyframes: {
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseSuccess: {
          '0%, 100%': { transform: 'scale(1)', opacity: '1' },
          '50%': { transform: 'scale(1.05)', opacity: '0.8' },
        },
      }
    },
  },
  plugins: [],
}
