# Firebase Setup Guide

## 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name (e.g., "mcq-app")
4. Enable Google Analytics (optional)
5. Click "Create project"

## 2. Enable Authentication

1. In your Firebase project, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Click "Save"

## 3. Create Firestore Database

1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location close to your users
5. Click "Done"

## 4. Get Firebase Configuration

1. Go to Project Settings (gear icon)
2. Scroll down to "Your apps"
3. Click "Web" icon (</>) to add a web app
4. Register your app with a name
5. Copy the Firebase configuration object

## 5. Update Configuration

Replace the placeholder values in `src/firebase/config.js` with your actual Firebase config:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-actual-sender-id",
  appId: "your-actual-app-id"
};
```

## 6. Create Admin Account

1. Run the app: `npm run dev`
2. Go to signup page and create account with email: `<EMAIL>`
3. Use password: `Password`
4. This account will have admin privileges

## 7. Firestore Security Rules (Optional for Production)

For production, update Firestore rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read exams
    match /exams/{examId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
    
    // Allow users to read/write their own exam results
    match /examResults/{resultId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

## Database Structure

### Exams Collection
```javascript
{
  title: "JavaScript Fundamentals",
  topic: "Programming",
  timeLimit: 30, // minutes
  questions: [
    {
      question: "What is JavaScript?",
      options: ["A programming language", "A markup language", "A database", "An operating system"],
      correctAnswer: 0 // index of correct option
    }
  ],
  createdAt: timestamp,
  createdBy: "admin"
}
```

### ExamResults Collection
```javascript
{
  userId: "user-uid",
  examId: "exam-id",
  answers: {
    0: 1, // question index: selected option index
    1: 2,
    // ...
  },
  score: 8,
  totalQuestions: 10,
  completedAt: timestamp,
  timeSpent: 1200 // seconds
}
```
