import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { DemoAuthProvider } from './context/DemoAuthContext';
import Navbar from './components/Navbar';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Dashboard from './pages/Dashboard';
import AdminPanel from './pages/AdminPanel';
import ExamList from './pages/ExamList';
import ExamDetails from './pages/ExamDetails';
import TakeExam from './pages/TakeExam';
import ExamResults from './pages/ExamResults';
import ViewAnswers from './pages/ViewAnswers';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';

function App() {
  // Toggle between Firebase and Demo mode
  const [isDemoMode, setIsDemoMode] = useState(true); // Start with demo mode for easy testing

  const AppContent = () => (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-whitish-plum-50 to-plum-100">
        {/* Mode Toggle */}
        <div className="fixed top-4 right-4 z-50">
          <button
            onClick={() => setIsDemoMode(!isDemoMode)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
              isDemoMode
                ? 'bg-green-500 text-white hover:bg-green-600'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isDemoMode ? '🎮 Demo Mode' : '🔥 Firebase Mode'}
          </button>
        </div>

        <Navbar />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/exams" element={
              <ProtectedRoute>
                <ExamList />
              </ProtectedRoute>
            } />
            <Route path="/exam/:id" element={
              <ProtectedRoute>
                <ExamDetails />
              </ProtectedRoute>
            } />
            <Route path="/exam/:id/take" element={
              <ProtectedRoute>
                <TakeExam />
              </ProtectedRoute>
            } />
            <Route path="/exam/:id/results" element={
              <ProtectedRoute>
                <ExamResults />
              </ProtectedRoute>
            } />
            <Route path="/exam/:id/answers" element={
              <ProtectedRoute>
                <ViewAnswers />
              </ProtectedRoute>
            } />
            <Route path="/admin" element={
              <AdminRoute>
                <AdminPanel />
              </AdminRoute>
            } />
          </Routes>
        </main>
      </div>
    </Router>
  );

  return isDemoMode ? (
    <DemoAuthProvider>
      <AppContent />
    </DemoAuthProvider>
  ) : (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
