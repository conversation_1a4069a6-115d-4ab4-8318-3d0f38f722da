import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Navbar from './components/Navbar';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Dashboard from './pages/Dashboard';
import AdminPanel from './pages/AdminPanel';
import ExamList from './pages/ExamList';
import ExamDetails from './pages/ExamDetails';
import TakeExam from './pages/TakeExam';
import ExamResults from './pages/ExamResults';
import ViewAnswers from './pages/ViewAnswers';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gradient-to-br from-whitish-plum-50 to-plum-100">
          <Navbar />
          <main className="container mx-auto px-4 py-8">
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } />
              <Route path="/exams" element={
                <ProtectedRoute>
                  <ExamList />
                </ProtectedRoute>
              } />
              <Route path="/exam/:id" element={
                <ProtectedRoute>
                  <ExamDetails />
                </ProtectedRoute>
              } />
              <Route path="/exam/:id/take" element={
                <ProtectedRoute>
                  <TakeExam />
                </ProtectedRoute>
              } />
              <Route path="/exam/:id/results" element={
                <ProtectedRoute>
                  <ExamResults />
                </ProtectedRoute>
              } />
              <Route path="/exam/:id/answers" element={
                <ProtectedRoute>
                  <ViewAnswers />
                </ProtectedRoute>
              } />
              <Route path="/admin" element={
                <AdminRoute>
                  <AdminPanel />
                </AdminRoute>
              } />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
