@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gradient-to-br from-whitish-plum-50 to-plum-100 min-h-screen;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-plum-500 to-plum-600 hover:from-plum-600 hover:to-plum-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-whitish-plum-400 to-whitish-plum-500 hover:from-whitish-plum-500 hover:to-whitish-plum-600 text-plum-800 font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200;
  }

  .btn-danger {
    @apply bg-gradient-to-r from-light-pink-500 to-light-pink-600 hover:from-light-pink-600 hover:to-light-pink-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-whitish-plum-200 p-6 hover:shadow-xl transition-all duration-300;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-whitish-plum-300 rounded-lg focus:ring-2 focus:ring-plum-500 focus:border-transparent bg-white/90 backdrop-blur-sm transition-all duration-200;
  }

  .question-card {
    @apply bg-white/90 backdrop-blur-sm rounded-xl shadow-md border border-whitish-plum-200 p-6 mb-6 hover:shadow-lg transition-all duration-300;
  }

  .option-button {
    @apply w-full text-left p-4 rounded-lg border-2 border-whitish-plum-300 bg-white/70 hover:bg-whitish-plum-100 hover:border-plum-400 transition-all duration-200 cursor-pointer;
  }

  .option-selected {
    @apply border-plum-500 bg-plum-50 text-plum-800;
  }

  .option-correct {
    @apply border-green-500 bg-green-50 text-green-800;
  }

  .option-incorrect {
    @apply border-red-500 bg-red-50 text-red-800;
  }
}
