import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from '../context/AuthContext';

function ExamDetails() {
  const { id } = useParams();
  const [exam, setExam] = useState(null);
  const [userResult, setUserResult] = useState(null);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useAuth();

  useEffect(() => {
    fetchExamDetails();
    fetchUserResult();
  }, [id, currentUser]);

  const fetchExamDetails = async () => {
    try {
      const examDoc = await getDoc(doc(db, 'exams', id));
      if (examDoc.exists()) {
        setExam({ id: examDoc.id, ...examDoc.data() });
      }
    } catch (error) {
      console.error('Error fetching exam details:', error);
    }
  };

  const fetchUserResult = async () => {
    try {
      const resultsQuery = query(
        collection(db, 'examResults'),
        where('userId', '==', currentUser.uid),
        where('examId', '==', id)
      );
      const resultsSnapshot = await getDocs(resultsQuery);
      if (!resultsSnapshot.empty) {
        setUserResult(resultsSnapshot.docs[0].data());
      }
    } catch (error) {
      console.error('Error fetching user result:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-plum-600"></div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="card text-center">
        <h2 className="text-2xl font-semibold text-plum-800 mb-4">Exam Not Found</h2>
        <p className="text-plum-600 mb-6">The exam you're looking for doesn't exist.</p>
        <Link to="/exams" className="btn-primary">
          Back to Exams
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="card animate-fade-in">
        <div className="flex items-center justify-between mb-6">
          <div>
            <span className="bg-plum-100 text-plum-800 text-sm font-semibold px-3 py-1 rounded">
              {exam.topic}
            </span>
            {userResult && (
              <span className="bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded ml-2">
                Completed
              </span>
            )}
          </div>
          <Link to="/exams" className="text-plum-600 hover:text-plum-800">
            ← Back to Exams
          </Link>
        </div>

        <h1 className="text-3xl font-bold text-plum-800 mb-6">{exam.title}</h1>

        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="space-y-4">
            <div className="flex items-center text-plum-700">
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Questions:</span>
              <span className="ml-2">{exam.questions?.length || 0}</span>
            </div>

            <div className="flex items-center text-plum-700">
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Time Limit:</span>
              <span className="ml-2">{exam.timeLimit} minutes</span>
            </div>

            <div className="flex items-center text-plum-700">
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="font-medium">Format:</span>
              <span className="ml-2">Multiple Choice</span>
            </div>
          </div>

          {userResult && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-800 mb-4">Your Result</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-green-700">Score:</span>
                  <span className="font-semibold text-green-800">
                    {userResult.score}/{userResult.totalQuestions}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700">Percentage:</span>
                  <span className="font-semibold text-green-800">
                    {Math.round((userResult.score / userResult.totalQuestions) * 100)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700">Completed:</span>
                  <span className="font-semibold text-green-800">
                    {new Date(userResult.completedAt.seconds * 1000).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="bg-plum-50 border border-plum-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-plum-800 mb-3">Instructions</h3>
          <ul className="space-y-2 text-plum-700">
            <li className="flex items-start">
              <span className="text-plum-500 mr-2">•</span>
              Read each question carefully before selecting your answer
            </li>
            <li className="flex items-start">
              <span className="text-plum-500 mr-2">•</span>
              You can change your answers before submitting
            </li>
            <li className="flex items-start">
              <span className="text-plum-500 mr-2">•</span>
              The exam will auto-submit when time runs out
            </li>
            <li className="flex items-start">
              <span className="text-plum-500 mr-2">•</span>
              You can review answers after completing the exam
            </li>
          </ul>
        </div>

        <div className="flex gap-4 justify-center">
          {userResult ? (
            <>
              <Link to={`/exam/${id}/results`} className="btn-secondary">
                View Results
              </Link>
              <Link to={`/exam/${id}/answers`} className="btn-primary">
                Review Answers
              </Link>
            </>
          ) : (
            <Link to={`/exam/${id}/take`} className="btn-primary text-lg px-8 py-4">
              Start Exam
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}

export default ExamDetails;
