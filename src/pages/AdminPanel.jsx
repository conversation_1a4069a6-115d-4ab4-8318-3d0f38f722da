import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where } from 'firebase/firestore';
import { db } from '../firebase/config';

function AdminPanel() {
  const [activeTab, setActiveTab] = useState('create'); // 'create', 'manage', 'analytics'
  const [exams, setExams] = useState([]);
  const [examResults, setExamResults] = useState([]);
  const [editingExam, setEditingExam] = useState(null);
  const [examData, setExamData] = useState({
    title: '',
    topic: '',
    timeLimit: 30,
    questions: []
  });
  const [questionsText, setQuestionsText] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (activeTab === 'manage' || activeTab === 'analytics') {
      fetchExams();
    }
    if (activeTab === 'analytics') {
      fetchExamResults();
    }
  }, [activeTab]);

  const fetchExams = async () => {
    try {
      const examsCollection = collection(db, 'exams');
      const examSnapshot = await getDocs(examsCollection);
      const examList = examSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setExams(examList);
    } catch (error) {
      console.error('Error fetching exams:', error);
    }
  };

  const fetchExamResults = async () => {
    try {
      const resultsCollection = collection(db, 'examResults');
      const resultsSnapshot = await getDocs(resultsCollection);
      const resultsList = resultsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setExamResults(resultsList);
    } catch (error) {
      console.error('Error fetching exam results:', error);
    }
  };

  const handleEditExam = (exam) => {
    setEditingExam(exam);
    setExamData({
      title: exam.title,
      topic: exam.topic,
      timeLimit: exam.timeLimit,
      questions: exam.questions
    });
    setQuestionsText(JSON.stringify(exam.questions, null, 2));
    setActiveTab('create');
  };

  const handleDeleteExam = async (examId) => {
    if (window.confirm('Are you sure you want to delete this exam? This action cannot be undone.')) {
      try {
        await deleteDoc(doc(db, 'exams', examId));
        setMessage('Exam deleted successfully!');
        fetchExams();

        // Also delete related results
        const resultsQuery = query(collection(db, 'examResults'), where('examId', '==', examId));
        const resultsSnapshot = await getDocs(resultsQuery);
        const deletePromises = resultsSnapshot.docs.map(doc => deleteDoc(doc.ref));
        await Promise.all(deletePromises);
      } catch (error) {
        setMessage(`Error deleting exam: ${error.message}`);
      }
    }
  };

  const handleDuplicateExam = async (exam) => {
    try {
      const duplicatedExam = {
        ...exam,
        title: `${exam.title} (Copy)`,
        createdAt: new Date(),
        createdBy: 'admin'
      };

      // Remove the id field
      delete duplicatedExam.id;
      delete duplicatedExam.updatedAt;

      await addDoc(collection(db, 'exams'), duplicatedExam);
      setMessage('Exam duplicated successfully!');
      fetchExams();
    } catch (error) {
      setMessage(`Error duplicating exam: ${error.message}`);
    }
  };

  const resetForm = () => {
    setEditingExam(null);
    setExamData({ title: '', topic: '', timeLimit: 30, questions: [] });
    setQuestionsText('');
    setMessage('');
  };

  const parseQuestions = (text) => {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(text);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      return [parsed];
    } catch {
      // If JSON parsing fails, parse as text format
      const lines = text.split('\n').filter(line => line.trim());
      const questions = [];
      let currentQuestion = null;

      for (let line of lines) {
        line = line.trim();
        if (line.startsWith('Q:') || line.match(/^\d+\./)) {
          if (currentQuestion) {
            questions.push(currentQuestion);
          }
          currentQuestion = {
            question: line.replace(/^(Q:|^\d+\.)/, '').trim(),
            options: [],
            correctAnswer: 0
          };
        } else if (line.match(/^[A-D]\)/)) {
          if (currentQuestion) {
            const optionText = line.substring(2).trim();
            currentQuestion.options.push(optionText);
            if (line.startsWith('A)')) currentQuestion.correctAnswer = 0;
            else if (line.startsWith('B)')) currentQuestion.correctAnswer = 1;
            else if (line.startsWith('C)')) currentQuestion.correctAnswer = 2;
            else if (line.startsWith('D)')) currentQuestion.correctAnswer = 3;
          }
        } else if (line.startsWith('Answer:') || line.startsWith('Correct:')) {
          if (currentQuestion) {
            const answer = line.split(':')[1].trim().toUpperCase();
            if (answer === 'A') currentQuestion.correctAnswer = 0;
            else if (answer === 'B') currentQuestion.correctAnswer = 1;
            else if (answer === 'C') currentQuestion.correctAnswer = 2;
            else if (answer === 'D') currentQuestion.correctAnswer = 3;
          }
        }
      }

      if (currentQuestion) {
        questions.push(currentQuestion);
      }

      return questions;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const questions = parseQuestions(questionsText);
      
      if (questions.length === 0) {
        throw new Error('No valid questions found');
      }

      if (editingExam) {
        // Update existing exam
        const examToUpdate = {
          ...examData,
          questions,
          updatedAt: new Date()
        };

        await updateDoc(doc(db, 'exams', editingExam.id), examToUpdate);
        setMessage('Exam updated successfully!');
      } else {
        // Create new exam
        const examToSave = {
          ...examData,
          questions,
          createdAt: new Date(),
          createdBy: 'admin'
        };

        await addDoc(collection(db, 'exams'), examToSave);
        setMessage('Exam created successfully!');
      }

      resetForm();
      if (activeTab === 'manage') {
        fetchExams();
      }
    } catch (error) {
      setMessage(`Error: ${error.message}`);
      console.error('Error creating exam:', error);
    }

    setLoading(false);
  };

  // Analytics calculations
  const getExamStats = () => {
    const examStats = exams.map(exam => {
      const examResultsForThisExam = examResults.filter(result => result.examId === exam.id);
      const totalAttempts = examResultsForThisExam.length;
      const averageScore = totalAttempts > 0
        ? examResultsForThisExam.reduce((sum, result) => sum + (result.score / result.totalQuestions * 100), 0) / totalAttempts
        : 0;

      return {
        ...exam,
        totalAttempts,
        averageScore: Math.round(averageScore)
      };
    });
    return examStats;
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8 animate-fade-in">
        <h1 className="text-4xl font-bold text-plum-800 mb-4">Admin Panel</h1>
        <p className="text-xl text-plum-600">Comprehensive exam management system</p>
      </div>

      {/* Tab Navigation */}
      <div className="flex justify-center mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-2 shadow-lg border border-whitish-plum-200">
          <button
            onClick={() => setActiveTab('create')}
            className={`px-6 py-3 rounded-lg font-semibold transition-all ${
              activeTab === 'create'
                ? 'bg-plum-500 text-white shadow-md'
                : 'text-plum-600 hover:bg-plum-50'
            }`}
          >
            {editingExam ? 'Edit Exam' : 'Create Exam'}
          </button>
          <button
            onClick={() => setActiveTab('manage')}
            className={`px-6 py-3 rounded-lg font-semibold transition-all ${
              activeTab === 'manage'
                ? 'bg-plum-500 text-white shadow-md'
                : 'text-plum-600 hover:bg-plum-50'
            }`}
          >
            Manage Exams
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`px-6 py-3 rounded-lg font-semibold transition-all ${
              activeTab === 'analytics'
                ? 'bg-plum-500 text-white shadow-md'
                : 'text-plum-600 hover:bg-plum-50'
            }`}
          >
            Analytics
          </button>
        </div>
      </div>

      {/* Create/Edit Exam Tab */}
      {activeTab === 'create' && (
        <div className="card animate-fade-in">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-plum-800">
              {editingExam ? 'Edit Exam' : 'Create New Exam'}
            </h2>
            {editingExam && (
              <button
                onClick={resetForm}
                className="btn-secondary"
              >
                Cancel Edit
              </button>
            )}
          </div>
        
        {message && (
          <div className={`p-4 rounded mb-6 ${
            message.startsWith('Error') 
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}>
            {message}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-plum-700 mb-2">
                Exam Title
              </label>
              <input
                type="text"
                value={examData.title}
                onChange={(e) => setExamData({...examData, title: e.target.value})}
                className="input-field"
                placeholder="e.g., JavaScript Fundamentals"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-plum-700 mb-2">
                Topic/Subject
              </label>
              <input
                type="text"
                value={examData.topic}
                onChange={(e) => setExamData({...examData, topic: e.target.value})}
                className="input-field"
                placeholder="e.g., Programming"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-plum-700 mb-2">
              Time Limit (minutes)
            </label>
            <input
              type="number"
              value={examData.timeLimit}
              onChange={(e) => setExamData({...examData, timeLimit: parseInt(e.target.value)})}
              className="input-field max-w-xs"
              min="1"
              max="180"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-plum-700 mb-2">
              Questions
            </label>
            <p className="text-sm text-plum-600 mb-3">
              You can enter questions in JSON format or text format:
            </p>
            
            <div className="bg-plum-50 p-4 rounded-lg mb-4 text-sm">
              <p className="font-medium text-plum-800 mb-2">Text Format Example:</p>
              <pre className="text-plum-700 whitespace-pre-wrap">
{`Q: What is the capital of France?
A) London
B) Paris
C) Berlin
D) Madrid
Answer: B

Q: Which programming language is this app built with?
A) Python
B) Java
C) JavaScript
D) C++
Answer: C`}
              </pre>
            </div>

            <textarea
              value={questionsText}
              onChange={(e) => setQuestionsText(e.target.value)}
              className="input-field h-64 font-mono text-sm"
              placeholder="Enter questions in JSON or text format..."
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="btn-primary w-full"
          >
            {loading
              ? (editingExam ? 'Updating Exam...' : 'Creating Exam...')
              : (editingExam ? 'Update Exam' : 'Create Exam')
            }
          </button>
        </form>
        </div>
      )}

      {/* Manage Exams Tab */}
      {activeTab === 'manage' && (
        <div className="space-y-6 animate-fade-in">
          <div className="card">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-plum-800">Manage Exams</h2>
              <div className="text-sm text-plum-600">
                Total Exams: {exams.length}
              </div>
            </div>

            {exams.length === 0 ? (
              <div className="text-center py-12">
                <svg className="w-16 h-16 text-plum-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="text-xl font-semibold text-plum-800 mb-2">No Exams Created</h3>
                <p className="text-plum-600 mb-4">Create your first exam to get started</p>
                <button
                  onClick={() => setActiveTab('create')}
                  className="btn-primary"
                >
                  Create First Exam
                </button>
              </div>
            ) : (
              <div className="grid gap-4">
                {exams.map((exam, index) => (
                  <div key={exam.id} className="bg-white/70 rounded-lg p-6 border border-whitish-plum-200 hover:shadow-md transition-all">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-plum-800">{exam.title}</h3>
                          <span className="bg-plum-100 text-plum-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                            {exam.topic}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-plum-600 mb-4">
                          <div>
                            <span className="font-medium">Questions:</span> {exam.questions?.length || 0}
                          </div>
                          <div>
                            <span className="font-medium">Time Limit:</span> {exam.timeLimit} min
                          </div>
                          <div>
                            <span className="font-medium">Created:</span> {new Date(exam.createdAt?.seconds * 1000 || exam.createdAt).toLocaleDateString()}
                          </div>
                          <div>
                            <span className="font-medium">Status:</span>
                            <span className="text-green-600 ml-1">Active</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2 ml-4">
                        <Link
                          to={`/admin/exam/${exam.id}/results`}
                          className="btn-primary text-sm"
                        >
                          View Results
                        </Link>
                        <button
                          onClick={() => handleEditExam(exam)}
                          className="btn-secondary text-sm"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDuplicateExam(exam)}
                          className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 text-sm"
                        >
                          Duplicate
                        </button>
                        <button
                          onClick={() => handleDeleteExam(exam.id)}
                          className="btn-danger text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="space-y-6 animate-fade-in">
          <div className="grid md:grid-cols-3 gap-6 mb-6">
            <div className="card text-center">
              <div className="text-3xl font-bold text-plum-600 mb-2">{exams.length}</div>
              <div className="text-plum-800 font-semibold">Total Exams</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">{examResults.length}</div>
              <div className="text-plum-800 font-semibold">Total Attempts</div>
            </div>
            <div className="card text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {examResults.length > 0
                  ? Math.round(examResults.reduce((sum, result) => sum + (result.score / result.totalQuestions * 100), 0) / examResults.length)
                  : 0
                }%
              </div>
              <div className="text-plum-800 font-semibold">Average Score</div>
            </div>
          </div>

          <div className="card">
            <h2 className="text-2xl font-semibold text-plum-800 mb-6">Exam Performance</h2>
            {getExamStats().length === 0 ? (
              <div className="text-center py-8">
                <p className="text-plum-600">No exam data available yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {getExamStats().map((exam) => (
                  <div key={exam.id} className="bg-plum-50 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h3 className="font-semibold text-plum-800">{exam.title}</h3>
                      <span className="text-sm text-plum-600">{exam.topic}</span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-plum-600">Questions:</span>
                        <div className="font-semibold text-plum-800">{exam.questions?.length || 0}</div>
                      </div>
                      <div>
                        <span className="text-plum-600">Attempts:</span>
                        <div className="font-semibold text-plum-800">{exam.totalAttempts}</div>
                      </div>
                      <div>
                        <span className="text-plum-600">Avg Score:</span>
                        <div className={`font-semibold ${
                          exam.averageScore >= 80 ? 'text-green-600' :
                          exam.averageScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {exam.averageScore}%
                        </div>
                      </div>
                      <div>
                        <span className="text-plum-600">Difficulty:</span>
                        <div className="font-semibold text-plum-800">
                          {exam.averageScore >= 80 ? 'Easy' :
                           exam.averageScore >= 60 ? 'Medium' : 'Hard'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {message && (
        <div className={`fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
          message.startsWith('Error')
            ? 'bg-red-100 border border-red-400 text-red-700'
            : 'bg-green-100 border border-green-400 text-green-700'
        }`}>
          {message}
        </div>
      )}
    </div>
  );
}

export default AdminPanel;
