import React, { useState } from 'react';
import { collection, addDoc } from 'firebase/firestore';
import { db } from '../firebase/config';

function AdminPanel() {
  const [examData, setExamData] = useState({
    title: '',
    topic: '',
    timeLimit: 30,
    questions: []
  });
  const [questionsText, setQuestionsText] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const parseQuestions = (text) => {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(text);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      return [parsed];
    } catch {
      // If JSON parsing fails, parse as text format
      const lines = text.split('\n').filter(line => line.trim());
      const questions = [];
      let currentQuestion = null;

      for (let line of lines) {
        line = line.trim();
        if (line.startsWith('Q:') || line.match(/^\d+\./)) {
          if (currentQuestion) {
            questions.push(currentQuestion);
          }
          currentQuestion = {
            question: line.replace(/^(Q:|^\d+\.)/, '').trim(),
            options: [],
            correctAnswer: 0
          };
        } else if (line.match(/^[A-D]\)/)) {
          if (currentQuestion) {
            const optionText = line.substring(2).trim();
            currentQuestion.options.push(optionText);
            if (line.startsWith('A)')) currentQuestion.correctAnswer = 0;
            else if (line.startsWith('B)')) currentQuestion.correctAnswer = 1;
            else if (line.startsWith('C)')) currentQuestion.correctAnswer = 2;
            else if (line.startsWith('D)')) currentQuestion.correctAnswer = 3;
          }
        } else if (line.startsWith('Answer:') || line.startsWith('Correct:')) {
          if (currentQuestion) {
            const answer = line.split(':')[1].trim().toUpperCase();
            if (answer === 'A') currentQuestion.correctAnswer = 0;
            else if (answer === 'B') currentQuestion.correctAnswer = 1;
            else if (answer === 'C') currentQuestion.correctAnswer = 2;
            else if (answer === 'D') currentQuestion.correctAnswer = 3;
          }
        }
      }

      if (currentQuestion) {
        questions.push(currentQuestion);
      }

      return questions;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const questions = parseQuestions(questionsText);
      
      if (questions.length === 0) {
        throw new Error('No valid questions found');
      }

      const examToSave = {
        ...examData,
        questions,
        createdAt: new Date(),
        createdBy: 'admin'
      };

      await addDoc(collection(db, 'exams'), examToSave);

      setMessage('Exam created successfully!');
      setExamData({ title: '', topic: '', timeLimit: 30, questions: [] });
      setQuestionsText('');
    } catch (error) {
      setMessage(`Error: ${error.message}`);
      console.error('Error creating exam:', error);
    }

    setLoading(false);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8 animate-fade-in">
        <h1 className="text-4xl font-bold text-plum-800 mb-4">Admin Panel</h1>
        <p className="text-xl text-plum-600">Create and manage exams</p>
      </div>

      <div className="card">
        <h2 className="text-2xl font-semibold text-plum-800 mb-6">Create New Exam</h2>
        
        {message && (
          <div className={`p-4 rounded mb-6 ${
            message.startsWith('Error') 
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}>
            {message}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-plum-700 mb-2">
                Exam Title
              </label>
              <input
                type="text"
                value={examData.title}
                onChange={(e) => setExamData({...examData, title: e.target.value})}
                className="input-field"
                placeholder="e.g., JavaScript Fundamentals"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-plum-700 mb-2">
                Topic/Subject
              </label>
              <input
                type="text"
                value={examData.topic}
                onChange={(e) => setExamData({...examData, topic: e.target.value})}
                className="input-field"
                placeholder="e.g., Programming"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-plum-700 mb-2">
              Time Limit (minutes)
            </label>
            <input
              type="number"
              value={examData.timeLimit}
              onChange={(e) => setExamData({...examData, timeLimit: parseInt(e.target.value)})}
              className="input-field max-w-xs"
              min="1"
              max="180"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-plum-700 mb-2">
              Questions
            </label>
            <p className="text-sm text-plum-600 mb-3">
              You can enter questions in JSON format or text format:
            </p>
            
            <div className="bg-plum-50 p-4 rounded-lg mb-4 text-sm">
              <p className="font-medium text-plum-800 mb-2">Text Format Example:</p>
              <pre className="text-plum-700 whitespace-pre-wrap">
{`Q: What is the capital of France?
A) London
B) Paris
C) Berlin
D) Madrid
Answer: B

Q: Which programming language is this app built with?
A) Python
B) Java
C) JavaScript
D) C++
Answer: C`}
              </pre>
            </div>

            <textarea
              value={questionsText}
              onChange={(e) => setQuestionsText(e.target.value)}
              className="input-field h-64 font-mono text-sm"
              placeholder="Enter questions in JSON or text format..."
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="btn-primary w-full"
          >
            {loading ? 'Creating Exam...' : 'Create Exam'}
          </button>
        </form>
      </div>
    </div>
  );
}

export default AdminPanel;
