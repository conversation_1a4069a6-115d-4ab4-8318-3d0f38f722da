import React from 'react';
import { Link } from 'react-router-dom';
import { useAuthMode } from '../hooks/useAuthMode';

function Dashboard() {
  const { currentUser, isAdmin } = useAuthMode();

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12 animate-fade-in">
        <h1 className="text-4xl font-bold text-plum-800 mb-4">
          Welcome to MCQ App
        </h1>
        <p className="text-xl text-plum-600 mb-2">
          Hello, {currentUser?.email}!
        </p>
        <p className="text-plum-500">
          Test your knowledge with our comprehensive exam system
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-12">
        <div className="card animate-slide-up">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-plum-500 to-plum-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-plum-800 mb-3">Take Exams</h3>
            <p className="text-plum-600 mb-6">
              Browse available exams and test your knowledge across various topics
            </p>
            <Link to="/exams" className="btn-primary">
              View Exams
            </Link>
          </div>
        </div>

        {isAdmin() && (
          <div className="card animate-slide-up" style={{ animationDelay: '0.1s' }}>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-light-pink-500 to-light-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-plum-800 mb-3">Admin Panel</h3>
              <p className="text-plum-600 mb-6">
                Create and manage exams, upload questions, and monitor results
              </p>
              <Link to="/admin" className="btn-danger">
                Admin Panel
              </Link>
            </div>
          </div>
        )}
      </div>

      <div className="card animate-slide-up" style={{ animationDelay: '0.2s' }}>
        <h3 className="text-2xl font-semibold text-plum-800 mb-6 text-center">Features</h3>
        <div className="grid md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-plum-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-plum-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h4 className="font-semibold text-plum-800 mb-2">Timed Exams</h4>
            <p className="text-sm text-plum-600">Take exams with time limits to simulate real test conditions</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-plum-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-plum-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h4 className="font-semibold text-plum-800 mb-2">Instant Results</h4>
            <p className="text-sm text-plum-600">Get immediate feedback with detailed score breakdown</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 bg-plum-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-plum-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <h4 className="font-semibold text-plum-800 mb-2">Review Answers</h4>
            <p className="text-sm text-plum-600">Review correct answers after completing exams</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
