import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { doc, getDoc, collection, addDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from '../context/AuthContext';
import QuestionPagination from '../components/QuestionPagination';

function TakeExam() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  
  const [exam, setExam] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(0);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchExam();
  }, [id]);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && exam) {
      handleSubmit();
    }
  }, [timeLeft, exam]);

  const fetchExam = async () => {
    try {
      const examDoc = await getDoc(doc(db, 'exams', id));
      if (examDoc.exists()) {
        const examData = { id: examDoc.id, ...examDoc.data() };
        setExam(examData);
        setTimeLeft(examData.timeLimit * 60); // Convert minutes to seconds
      }
    } catch (error) {
      console.error('Error fetching exam:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerSelect = (questionIndex, answerIndex) => {
    setAnswers({
      ...answers,
      [questionIndex]: answerIndex
    });
  };

  const handleSubmit = async () => {
    if (submitting) return;
    
    setSubmitting(true);
    
    try {
      // Calculate score
      let score = 0;
      exam.questions.forEach((question, index) => {
        if (answers[index] === question.correctAnswer) {
          score++;
        }
      });

      // Save result to Firestore
      const result = {
        userId: currentUser.uid,
        examId: id,
        answers,
        score,
        totalQuestions: exam.questions.length,
        completedAt: new Date(),
        timeSpent: (exam.timeLimit * 60) - timeLeft
      };

      await addDoc(collection(db, 'examResults'), result);
      
      // Navigate to results page
      navigate(`/exam/${id}/results`, { state: { result } });
    } catch (error) {
      console.error('Error submitting exam:', error);
      alert('Error submitting exam. Please try again.');
    }
    
    setSubmitting(false);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    if (timeLeft > 300) return 'text-green-600'; // > 5 minutes
    if (timeLeft > 60) return 'text-yellow-600';  // > 1 minute
    return 'text-red-600'; // < 1 minute
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-plum-600"></div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="card text-center">
        <h2 className="text-2xl font-semibold text-plum-800 mb-4">Exam Not Found</h2>
        <button onClick={() => navigate('/exams')} className="btn-primary">
          Back to Exams
        </button>
      </div>
    );
  }

  const currentQ = exam.questions[currentQuestion];
  const progress = ((currentQuestion + 1) / exam.questions.length) * 100;

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header with timer and progress */}
      <div className="card mb-6 animate-fade-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-plum-800">{exam.title}</h1>
            <p className="text-plum-600">
              Question {currentQuestion + 1} of {exam.questions.length}
            </p>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${getTimeColor()}`}>
              {formatTime(timeLeft)}
            </div>
            <p className="text-sm text-plum-600">Time Remaining</p>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="mt-4">
          <div className="bg-plum-200 rounded-full h-2">
            <div 
              className="bg-plum-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Question */}
      <div className="question-card animate-slide-up">
        <h2 className="text-xl font-semibold text-plum-800 mb-6">
          {currentQ.question}
        </h2>
        
        <div className="space-y-3">
          {currentQ.options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleAnswerSelect(currentQuestion, index)}
              className={`option-button ${
                answers[currentQuestion] === index ? 'option-selected' : ''
              }`}
            >
              <span className="font-semibold mr-3">
                {String.fromCharCode(65 + index)})
              </span>
              {option}
            </button>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <div className="mt-8">
        <QuestionPagination
          totalQuestions={exam.questions.length}
          currentQuestion={currentQuestion}
          answers={answers}
          onQuestionSelect={setCurrentQuestion}
        />

        {/* Submit Button */}
        {currentQuestion === exam.questions.length - 1 && (
          <div className="flex justify-center mt-6">
            <button
              onClick={handleSubmit}
              disabled={submitting}
              className="btn-primary text-lg px-8 py-4"
            >
              {submitting ? 'Submitting...' : 'Submit Exam'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default TakeExam;
