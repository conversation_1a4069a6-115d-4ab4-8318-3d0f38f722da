import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from '../context/AuthContext';

function ViewAnswers() {
  const { id } = useParams();
  const { currentUser } = useAuth();

  const [exam, setExam] = useState(null);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 5;

  useEffect(() => {
    fetchData();
  }, [id, currentUser]);

  const fetchData = async () => {
    try {
      // Fetch exam
      const examDoc = await getDoc(doc(db, 'exams', id));
      if (examDoc.exists()) {
        setExam({ id: examDoc.id, ...examDoc.data() });
      }

      // Fetch user result
      const resultsQuery = query(
        collection(db, 'examResults'),
        where('userId', '==', currentUser.uid),
        where('examId', '==', id)
      );
      const resultsSnapshot = await getDocs(resultsQuery);
      if (!resultsSnapshot.empty) {
        setResult(resultsSnapshot.docs[0].data());
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-plum-600"></div>
      </div>
    );
  }

  if (!exam || !result) {
    return (
      <div className="card text-center">
        <h2 className="text-2xl font-semibold text-plum-800 mb-4">
          {!result ? 'You must complete the exam first' : 'Data Not Found'}
        </h2>
        <Link to="/exams" className="btn-primary">
          Back to Exams
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="card mb-8 animate-fade-in">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-plum-800 mb-2">{exam.title}</h1>
            <p className="text-plum-600">Answer Review</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-plum-700">
              {result.score}/{result.totalQuestions}
            </div>
            <p className="text-sm text-plum-600">
              {Math.round((result.score / result.totalQuestions) * 100)}% Score
            </p>
          </div>
        </div>
      </div>

      {/* Questions and Answers */}
      <div className="space-y-6">
        {/* Pagination Info */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-plum-800">Review Your Answers</h2>
          <div className="text-sm text-plum-600">
            {exam.questions.length} question{exam.questions.length !== 1 ? 's' : ''} total
          </div>
        </div>

        {/* Questions */}
        {(() => {
          const totalPages = Math.ceil(exam.questions.length / questionsPerPage);
          const startIndex = (currentPage - 1) * questionsPerPage;
          const endIndex = startIndex + questionsPerPage;
          const currentQuestions = exam.questions.slice(startIndex, endIndex);

          return (
            <>
              {currentQuestions.map((question, localIndex) => {
                const questionIndex = startIndex + localIndex;
                const userAnswer = result.answers[questionIndex];
                const isCorrect = userAnswer === question.correctAnswer;
                const wasAnswered = userAnswer !== undefined;

                return (
                  <div
                    key={questionIndex}
                    className="question-card animate-slide-up"
                    style={{ animationDelay: `${questionIndex * 0.1}s` }}
                  >
                    {/* Question Header */}
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-plum-800">
                        Question {questionIndex + 1}
                      </h3>
                      <div className="flex items-center space-x-2">
                        {wasAnswered ? (
                          isCorrect ? (
                            <span className="bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded-full flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              Correct
                            </span>
                          ) : (
                            <span className="bg-red-100 text-red-800 text-sm font-semibold px-3 py-1 rounded-full flex items-center">
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                              Incorrect
                            </span>
                          )
                        ) : (
                          <span className="bg-gray-100 text-gray-800 text-sm font-semibold px-3 py-1 rounded-full">
                            Not Answered
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Question Text */}
                    <p className="text-plum-800 font-medium mb-6">{question.question}</p>

                    {/* Options */}
                    <div className="space-y-3">
                      {question.options.map((option, optionIndex) => {
                        const isUserAnswer = userAnswer === optionIndex;
                        const isCorrectAnswer = question.correctAnswer === optionIndex;

                        let optionClass = 'option-button ';

                        if (isCorrectAnswer) {
                          optionClass += 'option-correct ';
                        } else if (isUserAnswer && !isCorrect) {
                          optionClass += 'option-incorrect ';
                        }

                        return (
                          <div key={optionIndex} className={optionClass}>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <span className="font-semibold mr-3">
                                  {String.fromCharCode(65 + optionIndex)})
                                </span>
                                <span>{option}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                {isUserAnswer && (
                                  <span className="text-sm font-medium text-plum-700">
                                    Your Answer
                                  </span>
                                )}
                                {isCorrectAnswer && (
                                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                )}
                                {isUserAnswer && !isCorrect && (
                                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Explanation */}
                    {!wasAnswered && (
                      <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <p className="text-sm text-gray-700">
                          <span className="font-semibold">You didn't answer this question.</span>
                          The correct answer is <span className="font-semibold">
                            {String.fromCharCode(65 + question.correctAnswer)}) {question.options[question.correctAnswer]}
                          </span>
                        </p>
                      </div>
                    )}

                    {wasAnswered && !isCorrect && (
                      <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-700">
                          <span className="font-semibold">Incorrect.</span>
                          The correct answer is <span className="font-semibold">
                            {String.fromCharCode(65 + question.correctAnswer)}) {question.options[question.correctAnswer]}
                          </span>
                        </p>
                      </div>
                    )}
                  </div>
                );
              })}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-4 mt-8 pt-6 border-t border-plum-200">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="flex items-center space-x-2 px-4 py-2 bg-plum-500 text-white rounded-lg hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-2">
                    {(() => {
                      const getVisiblePages = () => {
                        if (totalPages <= 7) {
                          return Array.from({ length: totalPages }, (_, i) => i + 1);
                        }

                        const pages = [];
                        pages.push(1);

                        let start = Math.max(2, currentPage - 1);
                        let end = Math.min(totalPages - 1, currentPage + 1);

                        if (currentPage <= 3) {
                          start = 2;
                          end = Math.min(totalPages - 1, 5);
                        } else if (currentPage >= totalPages - 2) {
                          start = Math.max(2, totalPages - 4);
                          end = totalPages - 1;
                        }

                        if (start > 2) pages.push('...');

                        for (let i = start; i <= end; i++) {
                          if (i > 1 && i < totalPages && !pages.includes(i)) {
                            pages.push(i);
                          }
                        }

                        if (end < totalPages - 1) pages.push('...');

                        if (totalPages > 1 && !pages.includes(totalPages)) {
                          pages.push(totalPages);
                        }

                        return pages;
                      };

                      return getVisiblePages().map((page, index) => (
                        <button
                          key={index}
                          onClick={() => typeof page === 'number' && setCurrentPage(page)}
                          disabled={typeof page === 'string'}
                          className={`w-10 h-10 rounded-lg font-semibold transition-all ${
                            typeof page === 'string'
                              ? 'cursor-default text-plum-400'
                              : page === currentPage
                              ? 'bg-plum-600 text-white'
                              : 'bg-plum-200 text-plum-700 hover:bg-plum-300'
                          }`}
                        >
                          {page}
                        </button>
                      ));
                    })()}
                  </div>

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="flex items-center space-x-2 px-4 py-2 bg-plum-500 text-white rounded-lg hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span>Next</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              )}

              <div className="text-center text-sm text-plum-600 mt-4">
                Showing questions {startIndex + 1}-{Math.min(endIndex, exam.questions.length)} of {exam.questions.length}
              </div>
            </>
          );
        })()}
      </div>

      {/* Footer Actions */}
      <div className="flex gap-4 justify-center mt-12 animate-slide-up">
        <Link to={`/exam/${id}/results`} className="btn-secondary">
          View Results
        </Link>
        <Link to="/exams" className="btn-primary">
          Back to Exams
        </Link>
      </div>
    </div>
  );
}

export default ViewAnswers;
