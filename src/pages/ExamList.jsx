import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuthMode } from '../hooks/useAuthMode';

function ExamList() {
  const [exams, setExams] = useState([]);
  const [userResults, setUserResults] = useState({});
  const [loading, setLoading] = useState(true);
  const { currentUser, isDemoMode, getDemoExams, getDemoExamResults } = useAuthMode();

  useEffect(() => {
    fetchExams();
    fetchUserResults();
  }, [currentUser]);

  const fetchExams = async () => {
    try {
      if (isDemoMode && getDemoExams) {
        const examList = await getDemoExams();
        setExams(examList);
      } else {
        const examsCollection = collection(db, 'exams');
        const examSnapshot = await getDocs(examsCollection);
        const examList = examSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setExams(examList);
      }
    } catch (error) {
      console.error('Error fetching exams:', error);
    }
  };

  const fetchUserResults = async () => {
    try {
      if (isDemoMode && getDemoExamResults) {
        const userResultsList = await getDemoExamResults(currentUser.uid);
        const results = {};
        userResultsList.forEach(result => {
          results[result.examId] = result;
        });
        setUserResults(results);
      } else {
        const resultsQuery = query(
          collection(db, 'examResults'),
          where('userId', '==', currentUser.uid)
        );
        const resultsSnapshot = await getDocs(resultsQuery);
        const results = {};
        resultsSnapshot.docs.forEach(doc => {
          const data = doc.data();
          results[data.examId] = data;
        });
        setUserResults(results);
      }
    } catch (error) {
      console.error('Error fetching user results:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-plum-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-12 animate-fade-in">
        <h1 className="text-4xl font-bold text-plum-800 mb-4">Available Exams</h1>
        <p className="text-xl text-plum-600">Choose an exam to test your knowledge</p>
      </div>

      {exams.length === 0 ? (
        <div className="card text-center">
          <div className="py-12">
            <svg className="w-16 h-16 text-plum-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="text-xl font-semibold text-plum-800 mb-2">No Exams Available</h3>
            <p className="text-plum-600">Check back later for new exams!</p>
          </div>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {exams.map((exam, index) => {
            const hasCompleted = userResults[exam.id];
            return (
              <div 
                key={exam.id} 
                className="card hover:scale-105 animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex flex-col h-full">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <span className="bg-plum-100 text-plum-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                        {exam.topic}
                      </span>
                      {hasCompleted && (
                        <span className="bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                          Completed
                        </span>
                      )}
                    </div>
                    
                    <h3 className="text-xl font-semibold text-plum-800 mb-3">
                      {exam.title}
                    </h3>
                    
                    <div className="space-y-2 text-sm text-plum-600 mb-4">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {exam.questions?.length || 0} Questions
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {exam.timeLimit} minutes
                      </div>
                      {hasCompleted && (
                        <div className="flex items-center text-green-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Score: {hasCompleted.score}/{hasCompleted.totalQuestions}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Link 
                      to={`/exam/${exam.id}`}
                      className="btn-secondary flex-1 text-center"
                    >
                      View Details
                    </Link>
                    {hasCompleted ? (
                      <Link 
                        to={`/exam/${exam.id}/answers`}
                        className="btn-primary flex-1 text-center"
                      >
                        View Answers
                      </Link>
                    ) : (
                      <Link 
                        to={`/exam/${exam.id}/take`}
                        className="btn-primary flex-1 text-center"
                      >
                        Take Exam
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default ExamList;
