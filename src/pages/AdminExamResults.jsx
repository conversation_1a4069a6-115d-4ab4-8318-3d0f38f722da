import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';

function AdminExamResults() {
  const { examId } = useParams();
  const [exam, setExam] = useState(null);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchExamAndResults();
  }, [examId]);

  const fetchExamAndResults = async () => {
    try {
      // Fetch exam details
      const examDoc = await getDoc(doc(db, 'exams', examId));
      if (examDoc.exists()) {
        setExam({ id: examDoc.id, ...examDoc.data() });
      }

      // Fetch exam results
      const resultsQuery = query(
        collection(db, 'examResults'),
        where('examId', '==', examId)
      );
      const resultsSnapshot = await getDocs(resultsQuery);
      const resultsList = resultsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      // Sort by completion date (newest first)
      resultsList.sort((a, b) => {
        const dateA = a.completedAt?.seconds || 0;
        const dateB = b.completedAt?.seconds || 0;
        return dateB - dateA;
      });
      
      setResults(resultsList);
    } catch (error) {
      console.error('Error fetching exam results:', error);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (percentage) => {
    if (percentage >= 80) return 'bg-green-100';
    if (percentage >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-plum-600"></div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="card text-center">
        <h2 className="text-2xl font-semibold text-plum-800 mb-4">Exam Not Found</h2>
        <Link to="/admin" className="btn-primary">
          Back to Admin Panel
        </Link>
      </div>
    );
  }

  const averageScore = results.length > 0 
    ? Math.round(results.reduce((sum, result) => sum + (result.score / result.totalQuestions * 100), 0) / results.length)
    : 0;

  return (
    <div className="max-w-6xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-plum-800 mb-2">{exam.title}</h1>
          <p className="text-plum-600">Detailed Results & Analytics</p>
        </div>
        <Link to="/admin" className="btn-secondary">
          ← Back to Admin Panel
        </Link>
      </div>

      {/* Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        <div className="card text-center">
          <div className="text-2xl font-bold text-plum-600 mb-1">{results.length}</div>
          <div className="text-sm text-plum-700">Total Attempts</div>
        </div>
        <div className="card text-center">
          <div className={`text-2xl font-bold mb-1 ${getScoreColor(averageScore)}`}>
            {averageScore}%
          </div>
          <div className="text-sm text-plum-700">Average Score</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-blue-600 mb-1">{exam.questions?.length || 0}</div>
          <div className="text-sm text-plum-700">Questions</div>
        </div>
        <div className="card text-center">
          <div className="text-2xl font-bold text-purple-600 mb-1">{exam.timeLimit}</div>
          <div className="text-sm text-plum-700">Minutes</div>
        </div>
      </div>

      {/* Results Table */}
      <div className="card">
        <h2 className="text-xl font-semibold text-plum-800 mb-6">Individual Results</h2>
        
        {results.length === 0 ? (
          <div className="text-center py-12">
            <svg className="w-16 h-16 text-plum-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="text-lg font-semibold text-plum-800 mb-2">No Attempts Yet</h3>
            <p className="text-plum-600">Students haven't taken this exam yet.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-plum-200">
                  <th className="text-left py-3 px-4 font-semibold text-plum-800">Student</th>
                  <th className="text-center py-3 px-4 font-semibold text-plum-800">Score</th>
                  <th className="text-center py-3 px-4 font-semibold text-plum-800">Percentage</th>
                  <th className="text-center py-3 px-4 font-semibold text-plum-800">Time Spent</th>
                  <th className="text-center py-3 px-4 font-semibold text-plum-800">Completed</th>
                  <th className="text-center py-3 px-4 font-semibold text-plum-800">Grade</th>
                </tr>
              </thead>
              <tbody>
                {results.map((result, index) => {
                  const percentage = Math.round((result.score / result.totalQuestions) * 100);
                  const timeSpent = Math.floor(result.timeSpent / 60);
                  const completedDate = new Date(result.completedAt.seconds * 1000);
                  
                  return (
                    <tr key={result.id} className="border-b border-plum-100 hover:bg-plum-50 transition-colors">
                      <td className="py-4 px-4">
                        <div className="font-medium text-plum-800">
                          Student #{index + 1}
                        </div>
                        <div className="text-sm text-plum-600">
                          ID: {result.userId.substring(0, 8)}...
                        </div>
                      </td>
                      <td className="text-center py-4 px-4">
                        <span className="font-semibold text-plum-800">
                          {result.score}/{result.totalQuestions}
                        </span>
                      </td>
                      <td className="text-center py-4 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getScoreBgColor(percentage)} ${getScoreColor(percentage)}`}>
                          {percentage}%
                        </span>
                      </td>
                      <td className="text-center py-4 px-4 text-plum-700">
                        {timeSpent} min
                      </td>
                      <td className="text-center py-4 px-4 text-plum-700">
                        <div>{completedDate.toLocaleDateString()}</div>
                        <div className="text-xs text-plum-500">
                          {completedDate.toLocaleTimeString()}
                        </div>
                      </td>
                      <td className="text-center py-4 px-4">
                        <span className={`font-semibold ${getScoreColor(percentage)}`}>
                          {percentage >= 90 ? 'A+' :
                           percentage >= 80 ? 'A' :
                           percentage >= 70 ? 'B' :
                           percentage >= 60 ? 'C' :
                           percentage >= 50 ? 'D' : 'F'}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Performance Distribution */}
      {results.length > 0 && (
        <div className="card mt-6">
          <h2 className="text-xl font-semibold text-plum-800 mb-6">Performance Distribution</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {['A+ (90-100%)', 'A (80-89%)', 'B (70-79%)', 'C (60-69%)', 'F (0-59%)'].map((grade, index) => {
              const ranges = [[90, 100], [80, 89], [70, 79], [60, 69], [0, 59]];
              const [min, max] = ranges[index];
              const count = results.filter(result => {
                const percentage = (result.score / result.totalQuestions) * 100;
                return percentage >= min && percentage <= max;
              }).length;
              
              return (
                <div key={grade} className="text-center p-4 bg-plum-50 rounded-lg">
                  <div className="text-2xl font-bold text-plum-700 mb-1">{count}</div>
                  <div className="text-sm text-plum-600">{grade}</div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

export default AdminExamResults;
