import React, { useState, useEffect } from 'react';
import { useParams, useLocation, Link } from 'react-router-dom';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from '../context/AuthContext';

function ExamResults() {
  const { id } = useParams();
  const location = useLocation();
  const { currentUser } = useAuth();
  
  const [exam, setExam] = useState(null);
  const [result, setResult] = useState(location.state?.result || null);
  const [loading, setLoading] = useState(!result);

  useEffect(() => {
    fetchExam();
    if (!result) {
      fetchResult();
    }
  }, [id, currentUser]);

  const fetchExam = async () => {
    try {
      const examDoc = await getDoc(doc(db, 'exams', id));
      if (examDoc.exists()) {
        setExam({ id: examDoc.id, ...examDoc.data() });
      }
    } catch (error) {
      console.error('Error fetching exam:', error);
    }
  };

  const fetchResult = async () => {
    try {
      const resultsQuery = query(
        collection(db, 'examResults'),
        where('userId', '==', currentUser.uid),
        where('examId', '==', id)
      );
      const resultsSnapshot = await getDocs(resultsQuery);
      if (!resultsSnapshot.empty) {
        setResult(resultsSnapshot.docs[0].data());
      }
    } catch (error) {
      console.error('Error fetching result:', error);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreMessage = (percentage) => {
    if (percentage >= 90) return 'Excellent! Outstanding performance!';
    if (percentage >= 80) return 'Great job! Well done!';
    if (percentage >= 70) return 'Good work! Keep it up!';
    if (percentage >= 60) return 'Not bad! Room for improvement.';
    return 'Keep studying and try again!';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-plum-600"></div>
      </div>
    );
  }

  if (!exam || !result) {
    return (
      <div className="card text-center">
        <h2 className="text-2xl font-semibold text-plum-800 mb-4">Results Not Found</h2>
        <Link to="/exams" className="btn-primary">
          Back to Exams
        </Link>
      </div>
    );
  }

  const percentage = Math.round((result.score / result.totalQuestions) * 100);
  const timeSpent = Math.floor(result.timeSpent / 60);

  return (
    <div className="max-w-4xl mx-auto">
      {/* Celebration Animation */}
      <div className="text-center mb-8 animate-bounce-in">
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-plum-500 to-plum-600 rounded-full mb-4">
          <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h1 className="text-4xl font-bold text-plum-800 mb-2">Exam Completed!</h1>
        <p className="text-xl text-plum-600">{exam.title}</p>
      </div>

      {/* Score Card */}
      <div className="card text-center mb-8 animate-slide-up">
        <div className="mb-6">
          <div className={`text-6xl font-bold mb-2 ${getScoreColor(percentage)}`}>
            {percentage}%
          </div>
          <div className="text-2xl text-plum-700 mb-4">
            {result.score} out of {result.totalQuestions} correct
          </div>
          <p className={`text-lg font-semibold ${getScoreColor(percentage)}`}>
            {getScoreMessage(percentage)}
          </p>
        </div>

        {/* Progress Circle */}
        <div className="flex justify-center mb-6">
          <div className="relative w-32 h-32">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                strokeWidth="8"
                fill="transparent"
                className="text-plum-200"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="currentColor"
                strokeWidth="8"
                fill="transparent"
                strokeDasharray={`${percentage * 2.51} 251`}
                className={getScoreColor(percentage)}
                strokeLinecap="round"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`text-2xl font-bold ${getScoreColor(percentage)}`}>
                {percentage}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Details */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        <div className="card animate-slide-up" style={{ animationDelay: '0.1s' }}>
          <h3 className="text-xl font-semibold text-plum-800 mb-4">Exam Details</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-plum-600">Total Questions:</span>
              <span className="font-semibold text-plum-800">{result.totalQuestions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-plum-600">Correct Answers:</span>
              <span className="font-semibold text-green-600">{result.score}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-plum-600">Incorrect Answers:</span>
              <span className="font-semibold text-red-600">{result.totalQuestions - result.score}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-plum-600">Time Spent:</span>
              <span className="font-semibold text-plum-800">{timeSpent} minutes</span>
            </div>
          </div>
        </div>

        <div className="card animate-slide-up" style={{ animationDelay: '0.2s' }}>
          <h3 className="text-xl font-semibold text-plum-800 mb-4">Performance</h3>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-plum-600">Accuracy</span>
                <span className="font-semibold text-plum-800">{percentage}%</span>
              </div>
              <div className="bg-plum-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-1000 ${
                    percentage >= 80 ? 'bg-green-500' : 
                    percentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
            </div>
            
            <div className="pt-4">
              <p className="text-sm text-plum-600">
                Completed on: {new Date(result.completedAt.seconds * 1000).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 justify-center animate-slide-up" style={{ animationDelay: '0.3s' }}>
        <Link to="/exams" className="btn-secondary">
          Back to Exams
        </Link>
        <Link to={`/exam/${id}/answers`} className="btn-primary">
          Review Answers
        </Link>
      </div>
    </div>
  );
}

export default ExamResults;
