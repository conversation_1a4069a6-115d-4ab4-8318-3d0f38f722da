import { useContext } from 'react';
import { useAuth } from '../context/AuthContext';
import { useDemoAuth } from '../context/DemoAuthContext';

export function useAuthMode() {
  // Try demo auth first
  try {
    const demoAuth = useDemoAuth();
    console.log('Demo auth available:', demoAuth);
    if (demoAuth && demoAuth.isDemoMode) {
      return {
        ...demoAuth,
        isDemoMode: true
      };
    }
  } catch (error) {
    console.log('Demo auth not available:', error.message);
  }

  // Fall back to Firebase auth
  try {
    const firebaseAuth = useAuth();
    console.log('Firebase auth available:', firebaseAuth);
    return {
      ...firebaseAuth,
      isDemoMode: false
    };
  } catch (error) {
    console.log('No auth available:', error.message);
    // No auth available
    return {
      currentUser: null,
      login: () => Promise.reject({ message: 'No auth provider available' }),
      signup: () => Promise.reject({ message: 'No auth provider available' }),
      logout: () => Promise.resolve(),
      isAdmin: () => false,
      isDemoMode: false
    };
  }
}
