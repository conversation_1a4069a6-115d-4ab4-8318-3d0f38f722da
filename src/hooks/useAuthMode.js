import { useContext } from 'react';
import { useAuth } from '../context/AuthContext';
import { useDemoAuth } from '../context/DemoAuthContext';

export function useAuthMode() {
  // Try demo auth first
  try {
    const demoAuth = useDemoAuth();
    if (demoAuth && demoAuth.isDemoMode) {
      return {
        ...demoAuth,
        isDemoMode: true
      };
    }
  } catch (error) {
    // Demo auth not available
  }

  // Fall back to Firebase auth
  try {
    const firebaseAuth = useAuth();
    return {
      ...firebaseAuth,
      isDemoMode: false
    };
  } catch (error) {
    // No auth available
    return {
      currentUser: null,
      login: () => Promise.reject({ message: 'No auth provider available' }),
      signup: () => Promise.reject({ message: 'No auth provider available' }),
      logout: () => Promise.resolve(),
      isAdmin: () => false,
      isDemoMode: false
    };
  }
}
