import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCZZk7eZIza2pD4D3Tt-QMZWmUGZGmnV08",
  authDomain: "retro-app-2025.firebaseapp.com",
  projectId: "retro-app-2025",
  storageBucket: "retro-app-2025.firebasestorage.app",
  messagingSenderId: "358273281250",
  appId: "1:358273281250:web:1e07cd7bdb5817dbae42bb",
  measurementId: "G-G90WXSYPB9"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// For development: Connect to emulators if available
if (import.meta.env.DEV && window.location.hostname === 'localhost') {
  try {
    // Only connect if not already connected
    if (!auth._delegate._config.emulator) {
      connectAuthEmulator(auth, "http://localhost:9099", { disableWarnings: true });
    }
    if (!db._delegate._databaseId.projectId.includes('demo-')) {
      connectFirestoreEmulator(db, 'localhost', 8080);
    }
  } catch (error) {
    console.log('Emulators not available, using production Firebase');
  }
}

export default app;
