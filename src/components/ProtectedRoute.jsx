import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useDemoAuth } from '../context/DemoAuthContext';

function ProtectedRoute({ children }) {
  // Try to get auth from either context
  let currentUser, isDemo;

  try {
    const demoAuth = useDemoAuth();
    currentUser = demoAuth?.currentUser;
    isDemo = true;
  } catch {
    try {
      const firebaseAuth = useAuth();
      currentUser = firebaseAuth?.currentUser;
      isDemo = false;
    } catch {
      currentUser = null;
    }
  }

  return currentUser ? children : <Navigate to="/login" />;
}

export default ProtectedRoute;
