import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useDemoAuth } from '../context/DemoAuthContext';

function AdminRoute({ children }) {
  // Try to get auth from either context
  let currentUser, isAdmin;

  try {
    const demoAuth = useDemoAuth();
    currentUser = demoAuth?.currentUser;
    isAdmin = demoAuth?.isAdmin;
  } catch {
    try {
      const firebaseAuth = useAuth();
      currentUser = firebaseAuth?.currentUser;
      isAdmin = firebaseAuth?.isAdmin;
    } catch {
      currentUser = null;
      isAdmin = () => false;
    }
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (!isAdmin()) {
    return <Navigate to="/" />;
  }

  return children;
}

export default AdminRoute;
