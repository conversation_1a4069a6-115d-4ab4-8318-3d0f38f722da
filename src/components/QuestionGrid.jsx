import React, { useState } from 'react';

function QuestionGrid({ questions, onQuestionEdit, onQuestionDelete, readOnly = false }) {
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 10;
  
  const totalPages = Math.ceil(questions.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const endIndex = startIndex + questionsPerPage;
  const currentQuestions = questions.slice(startIndex, endIndex);

  const getPageNumbers = () => {
    const pages = [];
    const maxVisible = 5;
    
    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      
      if (currentPage > 3) {
        pages.push('...');
      }
      
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      
      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== totalPages) {
          pages.push(i);
        }
      }
      
      if (currentPage < totalPages - 2) {
        pages.push('...');
      }
      
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  if (questions.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-plum-600">No questions added yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Questions List */}
      <div className="space-y-4">
        {currentQuestions.map((question, index) => {
          const globalIndex = startIndex + index;
          return (
            <div key={globalIndex} className="bg-white/70 rounded-lg p-6 border border-whitish-plum-200">
              <div className="flex justify-between items-start mb-4">
                <h4 className="text-lg font-semibold text-plum-800">
                  Question {globalIndex + 1}
                </h4>
                {!readOnly && (
                  <div className="flex gap-2">
                    <button
                      onClick={() => onQuestionEdit && onQuestionEdit(globalIndex)}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => onQuestionDelete && onQuestionDelete(globalIndex)}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                )}
              </div>
              
              <p className="text-plum-700 mb-4 font-medium">{question.question}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {question.options.map((option, optionIndex) => (
                  <div
                    key={optionIndex}
                    className={`p-3 rounded-lg border-2 text-sm ${
                      optionIndex === question.correctAnswer
                        ? 'border-green-500 bg-green-50 text-green-800'
                        : 'border-plum-200 bg-plum-50 text-plum-700'
                    }`}
                  >
                    <span className="font-semibold mr-2">
                      {String.fromCharCode(65 + optionIndex)})
                    </span>
                    {option}
                    {optionIndex === question.correctAnswer && (
                      <span className="ml-2 text-green-600 font-semibold">✓ Correct</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-2 bg-plum-500 text-white rounded-lg hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <div className="flex space-x-1">
            {getPageNumbers().map((page, index) => (
              <button
                key={index}
                onClick={() => typeof page === 'number' && setCurrentPage(page)}
                disabled={typeof page === 'string'}
                className={`w-10 h-10 rounded-lg text-sm font-semibold transition-all ${
                  page === currentPage
                    ? 'bg-plum-600 text-white'
                    : typeof page === 'string'
                    ? 'text-plum-400 cursor-default'
                    : 'bg-plum-200 text-plum-700 hover:bg-plum-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
          
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-2 bg-plum-500 text-white rounded-lg hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}

      {/* Summary */}
      <div className="text-center text-sm text-plum-600">
        Showing {startIndex + 1}-{Math.min(endIndex, questions.length)} of {questions.length} questions
      </div>
    </div>
  );
}

export default QuestionGrid;
