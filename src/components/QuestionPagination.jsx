import React from 'react';

function QuestionPagination({ 
  totalQuestions, 
  currentQuestion, 
  answers, 
  onQuestionSelect 
}) {
  const getVisiblePages = () => {
    const total = totalQuestions;
    const current = currentQuestion + 1; // Convert to 1-based
    const maxVisible = 7; // Maximum number of page buttons to show

    if (total <= maxVisible) {
      // Show all pages if total is small
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    const pages = [];

    // Always show first page
    pages.push(1);

    // Calculate range around current page
    let start = Math.max(2, current - 1);
    let end = Math.min(total - 1, current + 1);

    // Adjust range to show more context
    if (current <= 3) {
      // If near beginning, show more pages at start
      start = 2;
      end = Math.min(total - 1, 5);
    } else if (current >= total - 2) {
      // If near end, show more pages at end
      start = Math.max(2, total - 4);
      end = total - 1;
    }

    // Add ellipsis after first page if needed
    if (start > 2) {
      pages.push('...');
    }

    // Add pages around current (avoid duplicates)
    for (let i = start; i <= end; i++) {
      if (i > 1 && i < total && !pages.includes(i)) {
        pages.push(i);
      }
    }

    // Add ellipsis before last page if needed
    if (end < total - 1) {
      pages.push('...');
    }

    // Always show last page if more than 1 page
    if (total > 1 && !pages.includes(total)) {
      pages.push(total);
    }

    return pages;
  };

  const getQuestionStatus = (questionIndex) => {
    if (questionIndex === currentQuestion) {
      return 'current';
    }
    if (answers[questionIndex] !== undefined) {
      return 'answered';
    }
    return 'unanswered';
  };

  const getButtonClass = (page, status) => {
    const baseClass = "w-10 h-10 rounded-lg text-sm font-semibold transition-all duration-200 ";
    
    if (typeof page === 'string') {
      // Ellipsis
      return baseClass + "cursor-default text-plum-400";
    }
    
    switch (status) {
      case 'current':
        return baseClass + "bg-plum-600 text-white shadow-lg scale-110";
      case 'answered':
        return baseClass + "bg-green-500 text-white hover:bg-green-600 hover:scale-105 cursor-pointer";
      case 'unanswered':
        return baseClass + "bg-plum-200 text-plum-700 hover:bg-plum-300 hover:scale-105 cursor-pointer";
      default:
        return baseClass + "bg-plum-200 text-plum-700 hover:bg-plum-300 hover:scale-105 cursor-pointer";
    }
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Question Status Legend */}
      <div className="flex items-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-plum-600 rounded"></div>
          <span className="text-plum-700">Current</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-500 rounded"></div>
          <span className="text-plum-700">Answered</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-plum-200 rounded"></div>
          <span className="text-plum-700">Unanswered</span>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex items-center space-x-4">
        {/* Previous Button */}
        <button
          onClick={() => onQuestionSelect(Math.max(0, currentQuestion - 1))}
          disabled={currentQuestion === 0}
          className="flex items-center space-x-2 px-4 py-2 bg-plum-500 text-white rounded-lg hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <span>Previous</span>
        </button>

        {/* Page Numbers */}
        <div className="flex items-center space-x-2">
          {visiblePages.map((page, index) => {
            if (typeof page === 'string') {
              return (
                <span key={index} className={getButtonClass(page)}>
                  ...
                </span>
              );
            }
            
            const questionIndex = page - 1; // Convert back to 0-based
            const status = getQuestionStatus(questionIndex);
            
            return (
              <button
                key={page}
                onClick={() => onQuestionSelect(questionIndex)}
                className={getButtonClass(page, status)}
                title={`Question ${page} - ${status}`}
              >
                {page}
              </button>
            );
          })}
        </div>

        {/* Next Button */}
        <button
          onClick={() => onQuestionSelect(Math.min(totalQuestions - 1, currentQuestion + 1))}
          disabled={currentQuestion === totalQuestions - 1}
          className="flex items-center space-x-2 px-4 py-2 bg-plum-500 text-white rounded-lg hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          <span>Next</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Progress Info */}
      <div className="text-center text-sm text-plum-600">
        <div className="font-medium">
          Question {currentQuestion + 1} of {totalQuestions}
        </div>
        <div className="text-xs mt-1">
          {Object.keys(answers).length} answered • {totalQuestions - Object.keys(answers).length} remaining
        </div>
      </div>
    </div>
  );
}

export default QuestionPagination;
