import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

function Navbar() {
  const { currentUser, logout, isAdmin } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out:', error);
    }
  };

  return (
    <nav className="bg-white/90 backdrop-blur-sm shadow-lg border-b border-whitish-plum-200">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="text-2xl font-bold text-plum-700 hover:text-plum-800 transition-colors">
            MCQ App
          </Link>
          
          {currentUser ? (
            <div className="flex items-center space-x-4">
              <Link 
                to="/exams" 
                className="text-plum-600 hover:text-plum-800 font-medium transition-colors"
              >
                Exams
              </Link>
              
              {isAdmin() && (
                <Link 
                  to="/admin" 
                  className="text-plum-600 hover:text-plum-800 font-medium transition-colors"
                >
                  Admin Panel
                </Link>
              )}
              
              <div className="flex items-center space-x-3">
                <span className="text-plum-700 font-medium">
                  {currentUser.email}
                </span>
                <button 
                  onClick={handleLogout}
                  className="btn-danger text-sm"
                >
                  Logout
                </button>
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-4">
              <Link 
                to="/login" 
                className="text-plum-600 hover:text-plum-800 font-medium transition-colors"
              >
                Login
              </Link>
              <Link 
                to="/signup" 
                className="btn-primary text-sm"
              >
                Sign Up
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
