import React, { useState } from 'react';

function QuestionOverview({ questions, title = "Questions Overview" }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 5;
  
  const totalPages = Math.ceil(questions.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const endIndex = startIndex + questionsPerPage;
  const currentQuestions = questions.slice(startIndex, endIndex);

  if (questions.length === 0) {
    return (
      <div className="bg-plum-50 border border-plum-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-plum-800 mb-2">{title}</h3>
        <p className="text-plum-600">No questions available.</p>
      </div>
    );
  }

  return (
    <div className="bg-plum-50 border border-plum-200 rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-plum-800">{title}</h3>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-plum-600">
            {questions.length} question{questions.length !== 1 ? 's' : ''}
          </span>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-plum-600 hover:text-plum-800 text-sm font-medium"
          >
            {isExpanded ? 'Hide' : 'Show'} Questions
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="space-y-4">
          {/* Questions List */}
          <div className="space-y-3">
            {currentQuestions.map((question, index) => {
              const globalIndex = startIndex + index;
              return (
                <div key={globalIndex} className="bg-white rounded-lg p-4 border border-whitish-plum-200">
                  <div className="flex items-start space-x-3">
                    <span className="flex-shrink-0 w-8 h-8 bg-plum-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      {globalIndex + 1}
                    </span>
                    <div className="flex-1 min-w-0">
                      <p className="text-plum-800 font-medium mb-2 line-clamp-2">
                        {question.question}
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {question.options.map((option, optionIndex) => (
                          <div
                            key={optionIndex}
                            className={`text-xs p-2 rounded border ${
                              optionIndex === question.correctAnswer
                                ? 'border-green-400 bg-green-50 text-green-700'
                                : 'border-plum-200 bg-plum-50 text-plum-600'
                            }`}
                          >
                            <span className="font-semibold">
                              {String.fromCharCode(65 + optionIndex)})
                            </span>{' '}
                            <span className="line-clamp-1">{option}</span>
                            {optionIndex === question.correctAnswer && (
                              <span className="ml-1 text-green-600">✓</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 pt-4 border-t border-plum-200">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-plum-500 text-white rounded hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Previous
              </button>
              
              <span className="text-sm text-plum-600">
                Page {currentPage} of {totalPages}
              </span>
              
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-plum-500 text-white rounded hover:bg-plum-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Next
              </button>
            </div>
          )}

          <div className="text-center text-xs text-plum-500 pt-2">
            Showing {startIndex + 1}-{Math.min(endIndex, questions.length)} of {questions.length} questions
          </div>
        </div>
      )}
    </div>
  );
}

export default QuestionOverview;
