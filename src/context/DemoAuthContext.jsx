import React, { createContext, useContext, useEffect, useState } from 'react';

const DemoAuthContext = createContext();

export function useDemoAuth() {
  return useContext(DemoAuthContext);
}

// Demo users for testing
const DEMO_USERS = [
  {
    uid: 'admin-uid',
    email: 'jay<PERSON><EMAIL>',
    password: 'Password'
  },
  {
    uid: 'user-uid',
    email: '<EMAIL>',
    password: 'password123'
  }
];

// Demo exams data
const DEMO_EXAMS = [
  {
    id: 'exam1',
    title: 'JavaScript Fundamentals',
    topic: 'Programming',
    timeLimit: 30,
    questions: [
      {
        question: 'What is JavaScript?',
        options: ['A programming language', 'A markup language', 'A database', 'An operating system'],
        correctAnswer: 0
      },
      {
        question: 'Which of the following is used to declare a variable in JavaScript?',
        options: ['var', 'let', 'const', 'All of the above'],
        correctAnswer: 3
      },
      {
        question: 'What does DOM stand for?',
        options: ['Document Object Model', 'Data Object Management', 'Dynamic Object Method', 'Document Oriented Model'],
        correctAnswer: 0
      },
      {
        question: 'Which method is used to add an element to the end of an array?',
        options: ['push()', 'pop()', 'shift()', 'unshift()'],
        correctAnswer: 0
      },
      {
        question: 'What is the correct way to write a JavaScript array?',
        options: ['var colors = "red", "green", "blue"', 'var colors = (1:"red", 2:"green", 3:"blue")', 'var colors = ["red", "green", "blue"]', 'var colors = 1 = ("red"), 2 = ("green"), 3 = ("blue")'],
        correctAnswer: 2
      }
    ],
    createdAt: new Date(),
    createdBy: 'admin'
  },
  {
    id: 'exam2',
    title: 'React Basics',
    topic: 'Frontend Development',
    timeLimit: 25,
    questions: [
      {
        question: 'What is React?',
        options: ['A JavaScript library', 'A database', 'A web server', 'An operating system'],
        correctAnswer: 0
      },
      {
        question: 'What is JSX?',
        options: ['JavaScript XML', 'Java Syntax Extension', 'JSON XML', 'JavaScript Extension'],
        correctAnswer: 0
      },
      {
        question: 'Which hook is used to manage state in functional components?',
        options: ['useEffect', 'useState', 'useContext', 'useReducer'],
        correctAnswer: 1
      }
    ],
    createdAt: new Date(),
    createdBy: 'admin'
  }
];

export function DemoAuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in from localStorage
    const savedUser = localStorage.getItem('demoUser');
    if (savedUser) {
      setCurrentUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  function signup(email, password) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Check if user already exists
        const existingUser = DEMO_USERS.find(user => user.email === email);
        if (existingUser) {
          reject({ code: 'auth/email-already-in-use', message: 'Email already in use' });
          return;
        }

        // Create new user
        const newUser = {
          uid: `user-${Date.now()}`,
          email,
          password
        };
        
        DEMO_USERS.push(newUser);
        const userForState = { uid: newUser.uid, email: newUser.email };
        setCurrentUser(userForState);
        localStorage.setItem('demoUser', JSON.stringify(userForState));
        resolve(userForState);
      }, 1000);
    });
  }

  function login(email, password) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const user = DEMO_USERS.find(u => u.email === email && u.password === password);
        if (user) {
          const userForState = { uid: user.uid, email: user.email };
          setCurrentUser(userForState);
          localStorage.setItem('demoUser', JSON.stringify(userForState));
          resolve(userForState);
        } else {
          reject({ code: 'auth/invalid-credential', message: 'Invalid credentials' });
        }
      }, 1000);
    });
  }

  function logout() {
    return new Promise((resolve) => {
      setTimeout(() => {
        setCurrentUser(null);
        localStorage.removeItem('demoUser');
        resolve();
      }, 500);
    });
  }

  function isAdmin() {
    return currentUser?.email === '<EMAIL>';
  }

  // Demo Firestore functions
  function getDemoExams() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(DEMO_EXAMS);
      }, 500);
    });
  }

  function addDemoExam(examData) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newExam = {
          id: `exam-${Date.now()}`,
          ...examData,
          createdAt: new Date(),
          createdBy: 'admin'
        };
        DEMO_EXAMS.push(newExam);
        resolve(newExam);
      }, 1000);
    });
  }

  function getDemoExamResults(userId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const results = JSON.parse(localStorage.getItem('demoExamResults') || '[]');
        const userResults = results.filter(result => result.userId === userId);
        resolve(userResults);
      }, 500);
    });
  }

  function saveDemoExamResult(result) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const results = JSON.parse(localStorage.getItem('demoExamResults') || '[]');
        const newResult = {
          ...result,
          id: `result-${Date.now()}`,
          completedAt: { seconds: Math.floor(Date.now() / 1000) }
        };
        results.push(newResult);
        localStorage.setItem('demoExamResults', JSON.stringify(results));
        resolve(newResult);
      }, 500);
    });
  }

  const value = {
    currentUser,
    signup,
    login,
    logout,
    isAdmin,
    // Demo data functions
    getDemoExams,
    addDemoExam,
    getDemoExamResults,
    saveDemoExamResult,
    isDemoMode: true
  };

  return (
    <DemoAuthContext.Provider value={value}>
      {!loading && children}
    </DemoAuthContext.Provider>
  );
}
