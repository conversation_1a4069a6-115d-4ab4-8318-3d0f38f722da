# MCQ App - Multiple Choice Question Exam System

A modern, responsive web application for conducting online multiple-choice exams built with React, Vite, Firebase, and Tailwind CSS.

## Features

### For Students
- **User Authentication** - Secure login/signup system
- **Exam Dashboard** - View available exams and your progress
- **Timed Exams** - Take exams with countdown timers
- **Instant Results** - Get immediate feedback with detailed scoring
- **Answer Review** - Review correct answers after completing exams
- **Progress Tracking** - Track your exam history and scores

### For Admins
- **Admin Panel** - Create and manage exams
- **Question Upload** - Support for JSON and text format question input
- **Exam Configuration** - Set time limits, topics, and difficulty levels
- **User Management** - Monitor student progress and results

### Design & UX
- **Beautiful Theme** - Plum, whitish-plum, and light pink color scheme
- **Smooth Animations** - Engaging transitions and micro-interactions
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **Modern UI** - Clean, intuitive interface with glassmorphism effects

## Tech Stack

- **Frontend**: React 18 + Vite
- **Styling**: Tailwind CSS with custom color palette
- **Backend**: Firebase (Firestore + Authentication)
- **Routing**: React Router DOM
- **Icons**: Heroicons (via Tailwind)

## Quick Start

### 1. Installation
```bash
npm install
```

### 2. Firebase Setup
Follow the detailed guide in [FIREBASE_SETUP.md](./FIREBASE_SETUP.md) to:
- Create a Firebase project
- Enable Authentication and Firestore
- Get your Firebase configuration
- Update `src/firebase/config.js` with your credentials

### 3. Run the Application
```bash
npm run dev
```

### 4. Admin Access
- Email: `<EMAIL>`
- Password: `Password`

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Navbar.jsx      # Navigation bar
│   ├── ProtectedRoute.jsx
│   └── AdminRoute.jsx
├── pages/              # Main application pages
│   ├── Login.jsx       # Authentication pages
│   ├── Signup.jsx
│   ├── Dashboard.jsx   # User dashboard
│   ├── ExamList.jsx    # Browse exams
│   ├── ExamDetails.jsx # Exam information
│   ├── TakeExam.jsx    # Exam interface
│   ├── ExamResults.jsx # Results display
│   ├── ViewAnswers.jsx # Answer review
│   └── AdminPanel.jsx  # Admin interface
├── context/            # React Context providers
│   └── AuthContext.jsx # Authentication state
├── firebase/           # Firebase configuration
│   └── config.js
└── styles/            # Global styles and Tailwind config
```

## Usage

### Creating Exams (Admin)
1. Login with admin credentials
2. Navigate to Admin Panel
3. Fill in exam details (title, topic, time limit)
4. Add questions in either format:

**Text Format:**
```
Q: What is the capital of France?
A) London
B) Paris
C) Berlin
D) Madrid
Answer: B
```

**JSON Format:**
```json
[
  {
    "question": "What is JavaScript?",
    "options": ["A programming language", "A markup language", "A database", "An OS"],
    "correctAnswer": 0
  }
]
```

### Taking Exams (Students)
1. Browse available exams
2. Click "Take Exam" to start
3. Answer questions within the time limit
4. Submit to see results
5. Review answers after completion

## Color Theme

The app uses a beautiful plum-based color palette:

- **Primary**: Plum shades (#d946ef to #701a75)
- **Secondary**: Whitish-plum (#fefcff to #bb5eff)
- **Accent**: Light pink (#fef7f7 to #812727)
- **Background**: Gradient from whitish-plum-50 to plum-100

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).
