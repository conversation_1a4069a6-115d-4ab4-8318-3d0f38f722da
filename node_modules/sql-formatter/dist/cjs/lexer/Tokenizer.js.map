{"version": 3, "file": "Tokenizer.js", "sourceRoot": "", "sources": ["../../../src/lexer/Tokenizer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAA8C;AAC9C,yDAA2C;AAE3C,8EAAkE;AAClE,iDAA8D;AAC9D,0CAA2D;AAC3D,yDAAmD;AAInD,MAAqB,SAAS;IAI5B,YAAoB,GAAqB,EAAU,WAAmB;QAAlD,QAAG,GAAH,GAAG,CAAkB;QAAU,gBAAW,GAAX,WAAW,CAAQ;QACpE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAEM,QAAQ,CAAC,KAAa,EAAE,mBAA+B;QAC5D,MAAM,KAAK,GAAG;YACZ,GAAG,IAAI,CAAC,iBAAiB;YACzB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,mBAAmB,CAAC;YACtD,GAAG,IAAI,CAAC,gBAAgB;SACzB,CAAC;QACF,MAAM,MAAM,GAAG,IAAI,4BAAe,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACtE,CAAC;IAED,mDAAmD;IACnD,8DAA8D;IACtD,sBAAsB,CAAC,GAAqB;;QAClD,OAAO,IAAI,CAAC,UAAU,CAAC;YACrB;gBACE,IAAI,EAAE,oBAAS,CAAC,aAAa;gBAC7B,KAAK,EACH,qFAAqF;aACxF;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,aAAa;gBAC7B,KAAK,EAAE,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,gCAAa,EAAE,CAAC,CAAC,CAAC,mBAAmB;aAC3E;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,YAAY;gBAC5B,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,MAAA,GAAG,CAAC,gBAAgB,mCAAI,CAAC,IAAI,CAAC,CAAC;aACzD;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,iBAAiB;gBACjC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;aACpC;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,MAAM;gBACtB,KAAK,EACH,qIAAqI;aACxI;YACD,6DAA6D;YAC7D,oFAAoF;YACpF;gBACE,IAAI,EAAE,oBAAS,CAAC,eAAe;gBAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,MAAA,GAAG,CAAC,eAAe,mCAAI,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC;gBACpE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,IAAI;gBACpB,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,GAAG;gBACnB,KAAK,EAAE,UAAU;gBACjB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,OAAO;gBACvB,KAAK,EAAE,cAAc;gBACrB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,KAAK;gBACrB,KAAK,EAAE,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;gBACvE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,eAAe;gBAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,UAAU,CAAC;gBAC9D,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,eAAe;gBAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,CAAC;gBAC7D,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,sBAAsB;gBACtC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,UAAU,CAAC;gBACpE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,IAAI;gBACpB,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,IAAI;gBACpB,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,IAAI;gBACpB,KAAK,EAAE,WAAW;gBAClB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,aAAa;gBAC7B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,UAAU,CAAC;gBAC5D,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,GAAG;gBACnB,KAAK,EAAE,UAAU;gBACjB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,EAAE;gBAClB,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,GAAG;gBACnB,KAAK,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;gBAC/C,IAAI,EAAE,WAAW;aAClB;YACD,GAAG,CAAC,GAAG,CAAC,eAAe;gBACrB,CAAC,CAAC;oBACE;wBACE,IAAI,EAAE,oBAAS,CAAC,QAAQ;wBACxB,KAAK,EAAE,wBAAwB;qBAChC;iBACF;gBACH,CAAC,CAAC,EAAE,CAAC;YACP;gBACE,IAAI,EAAE,oBAAS,CAAC,sBAAsB;gBACtC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,UAAU,CAAC;gBACpE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,kBAAkB;gBAClC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,UAAU,CAAC;gBAChE,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,gBAAgB;gBAChC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,UAAU,CAAC;gBAC/D,IAAI,EAAE,WAAW;aAClB;SACF,CAAC,CAAC;IACL,CAAC;IAED,wDAAwD;IACxD,8DAA8D;IACtD,qBAAqB,CAAC,GAAqB;;QACjD,OAAO,IAAI,CAAC,UAAU,CAAC;YACrB;gBACE,IAAI,EAAE,oBAAS,CAAC,QAAQ;gBACxB,KAAK,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS;aACzE;YACD,EAAE,IAAI,EAAE,oBAAS,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YAChE;gBACE,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC;aACxC;YACD,EAAE,IAAI,EAAE,oBAAS,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;YAC7C,EAAE,IAAI,EAAE,oBAAS,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE;YACxC;gBACE,IAAI,EAAE,oBAAS,CAAC,UAAU;gBAC1B,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC;aAClD;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,WAAW;gBAC3B,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC;aACnD;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,QAAQ;gBACxB,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;oBACpB,qBAAqB;oBACrB,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,GAAG,CAAC,MAAA,GAAG,CAAC,SAAS,mCAAI,EAAE,CAAC;iBACzB,CAAC;aACH;YACD,EAAE,IAAI,EAAE,oBAAS,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;YAC5C;gBACE,IAAI,EAAE,oBAAS,CAAC,wBAAwB;gBACxC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,MAAA,GAAG,CAAC,uBAAuB,mCAAI,EAAE,CAAC,CAAC,CAAC;aACrE;SACF,CAAC,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,0DAA0D;IAClD,eAAe,CAAC,GAAqB,EAAE,mBAA+B;;QAC5E,6DAA6D;QAC7D,yDAAyD;QACzD,MAAM,UAAU,GAAG;YACjB,KAAK,EAAE,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,KAAK,MAAI,MAAA,GAAG,CAAC,UAAU,0CAAE,KAAK,CAAA,IAAI,EAAE;YAChE,MAAM,EAAE,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,MAAI,MAAA,GAAG,CAAC,UAAU,0CAAE,MAAM,CAAA,IAAI,EAAE;YACnE,QAAQ,EAAE,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,QAAQ,MAAI,MAAA,GAAG,CAAC,UAAU,0CAAE,QAAQ,CAAA,IAAI,EAAE;YACzE,UAAU,EACR,OAAO,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,UAAU,CAAA,KAAK,SAAS;gBAClD,CAAC,CAAC,mBAAmB,CAAC,UAAU;gBAChC,CAAC,CAAC,MAAA,GAAG,CAAC,UAAU,0CAAE,UAAU;YAChC,MAAM,EAAE,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,MAAI,MAAA,GAAG,CAAC,UAAU,0CAAE,MAAM,CAAA,IAAI,EAAE;SACpE,CAAC;QAEF,OAAO,IAAI,CAAC,UAAU,CAAC;YACrB;gBACE,IAAI,EAAE,oBAAS,CAAC,eAAe;gBAC/B,KAAK,EAAE,KAAK,CAAC,SAAS,CACpB,UAAU,CAAC,KAAK,EAChB,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,CAAC,CAC1D;gBACD,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;aACrB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,gBAAgB;gBAChC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC9E,GAAG,EAAE,CAAC,CAAC,EAAE,CACP,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,CAC3B,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAA,2BAAY,EAAC,IAAI,GAAG,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC/E,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxB,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBACvB,CAAC;aACL;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,kBAAkB;gBAClC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC;gBACrD,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;aACrB;YACD;gBACE,IAAI,EAAE,oBAAS,CAAC,oBAAoB;gBACpC,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aAClD;YACD,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CACtB,CAAC,WAAW,EAAa,EAAE;;gBAAC,OAAA,CAAC;oBAC3B,IAAI,EAAE,oBAAS,CAAC,gBAAgB;oBAChC,KAAK,EAAE,IAAA,6BAAc,EAAC,WAAW,CAAC,KAAK,CAAC;oBACxC,GAAG,EAAE,MAAA,WAAW,CAAC,GAAG,mCAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACjC,CAAC,CAAA;aAAA,CACH;SACF,CAAC,CAAC;IACL,CAAC;IAED,6DAA6D;IACrD,UAAU,CAAC,KAA0B;QAC3C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAqB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACxE,CAAC;CACF;AA9PD,4BA8PC;AAED;;;;GAIG;AACH,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,IAAA,6BAAkB,EAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC"}