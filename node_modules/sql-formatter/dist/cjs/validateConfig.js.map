{"version": 3, "file": "validateConfig.js", "sourceRoot": "", "sources": ["../../src/validateConfig.ts"], "names": [], "mappings": ";;;AAIA,MAAa,WAAY,SAAQ,KAAK;CAAG;AAAzC,kCAAyC;AAEzC,SAAgB,cAAc,CAAC,GAAkB;IAC/C,MAAM,cAAc,GAAG;QACrB,gBAAgB;QAChB,wBAAwB;QACxB,yBAAyB;QACzB,SAAS;QACT,eAAe;QACf,eAAe;KAChB,CAAC;IACF,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE;QACvC,IAAI,UAAU,IAAI,GAAG,EAAE;YACrB,MAAM,IAAI,WAAW,CAAC,GAAG,UAAU,+BAA+B,CAAC,CAAC;SACrE;KACF;IAED,IAAI,GAAG,CAAC,eAAe,IAAI,CAAC,EAAE;QAC5B,MAAM,IAAI,WAAW,CACnB,4DAA4D,GAAG,CAAC,eAAe,WAAW,CAC3F,CAAC;KACH;IAED,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QAC7C,sCAAsC;QACtC,OAAO,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;KACxE;IAED,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACzD,MAAM,IAAI,WAAW,CACnB,sGAAsG,CACvG,CAAC;KACH;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAjCD,wCAiCC;AAED,SAAS,cAAc,CAAC,MAA6B;IACnD,MAAM,WAAW,GAAG,MAAM,YAAY,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7E,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,kBAAkB,CAAC,UAAsB;IAChD,IAAI,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QACzD,OAAO,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;KACrD;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}