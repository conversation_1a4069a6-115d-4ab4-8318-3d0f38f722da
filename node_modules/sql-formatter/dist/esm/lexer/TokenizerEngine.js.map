{"version": 3, "file": "TokenizerEngine.js", "sourceRoot": "", "sources": ["../../../src/lexer/TokenizerEngine.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAkBlD,MAAM,CAAC,OAAO,OAAO,eAAe;IAIlC,YAAoB,KAAkB,EAAU,WAAmB;QAA/C,UAAK,GAAL,KAAK,CAAa;QAAU,gBAAW,GAAX,WAAW,CAAQ;QAH3D,UAAK,GAAG,EAAE,CAAC,CAAC,kCAAkC;QAC9C,UAAK,GAAG,CAAC,CAAC,CAAC,6BAA6B;IAEsB,CAAC;IAEvE;;;;;;OAMG;IACI,QAAQ,CAAC,KAAa;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,IAAI,KAAwB,CAAC;QAE7B,kDAAkD;QAClD,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrC,gCAAgC;YAChC,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAEjD,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBAClC,wCAAwC;gBACxC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,EAAE;oBACV,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;iBAC/B;gBAED,MAAM,CAAC,IAAI,iCAAM,KAAK,KAAE,mBAAmB,IAAG,CAAC;aAChD;SACF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,gBAAgB;QACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;QAC3D,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,OAAO,IAAI,KAAK,CACd,4BAA4B,IAAI,aAAa,IAAI,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,CAC1F,CAAC;IACJ,CAAC;IAEO,WAAW;QACjB,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;YAC9B,OAAO,CACL,uEAAuE;gBACvE,oFAAoF,CACrF,CAAC;SACH;aAAM;YACL,OAAO,sBAAsB,IAAI,CAAC,WAAW,IAAI,CAAC;SACnD;IACH,CAAC;IAEO,aAAa;QACnB,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;QAExC,MAAM,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE;YACX,wDAAwD;YACxD,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAChC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,YAAY;QAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,kEAAkE;IAC1D,KAAK,CAAC,IAAe;QAC3B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE;YACX,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAE/B,MAAM,KAAK,GAAU;gBACnB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG,EAAE,WAAW;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW;gBACtD,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC;YAEF,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;aACnC;YAED,mDAAmD;YACnD,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,MAAM,CAAC;YACjC,OAAO,KAAK,CAAC;SACd;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF"}